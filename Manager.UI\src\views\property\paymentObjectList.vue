<template>
  <div class="payment-object-list-container">
    <div class="payment-object-list-content">
      <payment-object-edit @search="search" ref="editDialog" />
      <payment-detail-dialog ref="paymentDetailDialog" />
      <div class="card card--search search-flex">
        <el-input v-model="searchModel.moniker" placeholder="对象名称" clearable style="width: 200px; margin-right: 16px;" />
        <el-input v-model="searchModel.phone" placeholder="手机号" clearable style="width: 200px; margin-right: 16px;" />
        <el-input v-model="searchModel.email" placeholder="邮箱" clearable style="width: 200px; margin-right: 16px;" />
        <el-select v-model="searchModel.communityId" placeholder="选择小区" clearable style="width: 200px; margin-right: 16px;" filterable>
          <el-option 
            v-for="item in communityList" 
            :key="item.id" 
            :label="item.communityName" 
            :value="item.id" />
        </el-select>
        <el-button type="primary" @click="search" style="margin-right: 8px;">搜索</el-button>
        <el-button type="primary" @click="add">添加</el-button>
      </div>
      <div class="card card--table">
        <div class="table-col">
          <el-table :data="paymentObjectList" row-key="id" style="width: 100%; height: 100%;" class="data-table">
            <el-table-column prop="id" label="ID" width="80" align="center"/>
            <el-table-column prop="moniker" label="对象名称" align="center" min-width="120"/>
            <el-table-column prop="phone" label="手机号" align="center" width="130"/>
            <el-table-column prop="email" label="邮箱" align="center" min-width="150" show-overflow-tooltip/>
            <el-table-column prop="address" label="地址" align="center" min-width="200" show-overflow-tooltip/>
            <el-table-column prop="residentId" label="关联住户" align="center" width="100">
              <template #default="scope">
                <el-tag v-if="scope.row.residentId" type="success" size="small">已关联</el-tag>
                <el-tag v-else type="info" size="small">未关联</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="communityName" label="所属小区" align="center" min-width="120"/>
            <el-table-column prop="createTime" label="创建时间" align="center" width="160"/>
            <el-table-column prop="updateTime" label="修改时间" align="center" width="160"/>
            <el-table-column label="操作" width="260" fixed="right">
              <template #default="scope">
                <el-button type="text" size="mini" @click="edit(scope.row.id)">编辑</el-button>
                <el-button type="text" size="mini" @click="viewPaymentDetails(scope.row)">缴费明细</el-button>
                <el-button type="text" size="mini" @click="deleted(scope.row.id)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div class="pagination-col">
          <el-pagination background layout="prev, pager, next" @current-change="currentChange" 
            :total="total" :page-size="searchModel.pageSize" :current-page="searchModel.pageNum" />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { listPropertyPaymentObject, deletePropertyPaymentObject, getPropertyPaymentObject } from '@/api/property/paymentItems'
import { listCommunity } from '@/api/community/community'
import mitt from '@/utils/mitt'
import paymentObjectEdit from '@/components/property/paymentObjectEdit.vue'
import paymentDetailDialog from '@/components/property/paymentDetailDialog.vue'

export default {
  components: { paymentObjectEdit, paymentDetailDialog },
  data() {
    return {
      searchModel: {
        pageNum: 1,
        pageSize: 10,
        moniker: '',
        phone: '',
        email: '',
        communityId: '',
        residentId: ''
      },
      paymentObjectList: [],
      communityList: [],
      total: 0
    }
  },
  methods: {
    search() {
      // 过滤空值参数
      const params = {}
      Object.keys(this.searchModel).forEach(key => {
        if (this.searchModel[key] !== '' && this.searchModel[key] !== null && this.searchModel[key] !== undefined) {
          params[key] = this.searchModel[key]
        }
      })
      
      listPropertyPaymentObject(params).then(res => {
        this.paymentObjectList = res.data.data.list || []
        this.total = res.data.data.total || 0
        
        // 补充小区名称显示
        this.paymentObjectList.forEach(item => {
          const community = this.communityList.find(c => c.id === item.communityId)
          item.communityName = community ? community.communityName : '--'
        })
      }).catch(err => {
        this.$message.error(err.data?.errorMessage || '查询失败')
      })
    },
    
    loadCommunityList() {
      listCommunity({ pageNum: 1, pageSize: 500 }).then(res => {
        this.communityList = res.data.data.list || []
      }).catch(err => {
        console.error('加载小区列表失败:', err)
      })
    },
    
    add() {
      mitt.emit('openPaymentObjectAdd')
    },
    
    edit(id) {
      getPropertyPaymentObject(id).then(res => {
        mitt.emit('openPaymentObjectEdit', res.data.data)
      }).catch(err => {
        this.$message.error(err.data?.errorMessage || '获取数据失败')
      })
    },

    viewPaymentDetails(row) {
      mitt.emit('openPaymentDetailDialog', row)
    },
    
    deleted(id) {
      this.$confirm('删除缴费对象, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deletePropertyPaymentObject(id).then(() => {
          this.search()
          this.$message.success('操作成功')
        }).catch(err => {
          this.$message.error(err.data?.errorMessage || '删除失败')
        })
      }).catch(() => {})
    },
    
    currentChange(num) {
      this.searchModel.pageNum = num
      this.search()
    }
  },
  
  created() {
    this.loadCommunityList()
    this.search()
  }
}
</script>

<style scoped>
::v-deep .--el-table-index {
  z-index: 9999 !important;
}

.payment-object-list-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  box-sizing: border-box;
}

.payment-object-list-content {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.table-col {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.data-table {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100% !important;
}

.pagination-col {
  display: flex;
  justify-content: flex-end;
  margin-top: 10px;
}

.dark-theme .card {
  background-color: var(--card-background);
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.3);
}

.search-flex {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 8px;
}
</style>
