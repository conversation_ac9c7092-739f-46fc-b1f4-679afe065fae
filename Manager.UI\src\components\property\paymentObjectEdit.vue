<template>
  <el-dialog :title="dialog.title" v-model="dialog.show" width="700px" top="5vh" :close-on-click-modal="false" class="payment-object-edit-dialog">
    <el-form :model="paymentObjectModel" :rules="rules" ref="formRef" label-width="120px" class="payment-object-edit-form">
      <el-row :gutter="16">
        <el-col :span="12">
          <el-form-item label="对象类型" prop="objectType">
            <el-radio-group v-model="objectType" @change="onObjectTypeChange">
              <el-radio label="resident">关联住户</el-radio>
              <el-radio label="custom">自定义对象</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>
      
      <!-- 住户选择 -->
      <el-row :gutter="16" v-if="objectType === 'resident'">
        <el-col :span="24">
          <el-form-item label="选择住户" prop="residentId">
            <div class="resident-selector">
              <el-input v-model="residentDisplay" placeholder="请选择住户" readonly style="width: calc(100% - 100px);" />
              <el-button type="primary" @click="openResidentSelector" style="margin-left: 10px;">选择住户</el-button>
            </div>
          </el-form-item>
        </el-col>
      </el-row>
      
      <!-- 自定义对象信息 -->
      <el-row :gutter="16" v-if="objectType === 'custom'">
        <el-col :span="12">
          <el-form-item label="对象名称" prop="moniker">
            <el-input v-model="paymentObjectModel.moniker" maxlength="50" placeholder="请输入对象名称" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="手机号" prop="phone">
            <el-input v-model="paymentObjectModel.phone" maxlength="11" placeholder="请输入手机号" clearable />
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="16">
        <el-col :span="12">
          <el-form-item label="邮箱" prop="email">
            <el-input v-model="paymentObjectModel.email" maxlength="100" placeholder="请输入邮箱" clearable />
          </el-form-item>
        </el-col>
 
      </el-row>
      
      <el-row :gutter="16">
        <el-col :span="24">
          <el-form-item label="地址" prop="address">
            <el-input v-model="paymentObjectModel.address" maxlength="200" placeholder="请输入地址" clearable />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    
    <!-- 住户选择弹窗 -->
    <el-dialog title="选择住户" v-model="residentSelectorDialog" width="800px" append-to-body>
      <div class="resident-search">
        <el-input v-model="residentSearchKeyword" placeholder="搜索住户姓名或手机号" clearable style="width: 300px; margin-bottom: 16px;" @input="searchResidents" />
      </div>
      <el-table :data="residentList" @row-click="selectResident" style="cursor: pointer;" max-height="400">
        <el-table-column prop="residentName" label="姓名" width="120" align="center"/>
        <el-table-column prop="phone" label="手机号" width="130" align="center"/>
        <el-table-column prop="address" label="地址" align="center" show-overflow-tooltip/>
        <el-table-column prop="residentType" label="住户类型" width="100" align="center"/>
      </el-table>
      <div class="pagination-col" style="margin-top: 16px;">
        <el-pagination background layout="prev, pager, next" @current-change="residentPageChange" 
          :total="residentTotal" :page-size="residentSearchModel.pageSize" :current-page="residentSearchModel.pageNum" />
      </div>
    </el-dialog>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialog.show = false">取消</el-button>
        <el-button type="primary" @click="submit" :loading="loading">保存</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script>
import { addPropertyPaymentObject, editPropertyPaymentObject } from '@/api/property/paymentItems'
import { listCommunity } from '@/api/community/community'
import { listCommunityResident } from '@/api/community/communityResident'
import { getSelectedCommunityId } from '@/store/modules/options'
import mitt from '@/utils/mitt'

export default {
  name: 'paymentObjectEdit',
  data() {
    return {
      loading: false,
      paymentObjectModel: {
        id: undefined,
        moniker: '',
        phone: '',
        address: '',
        communityId: null,
        email: '',
        residentId: null
      },
      dialog: {
        show: false,
        title: ''
      },
      objectType: 'custom', // resident: 关联住户, custom: 自定义对象
      residentDisplay: '', // 住户显示信息
      residentSelectorDialog: false,
      residentSearchKeyword: '',
      residentList: [],
      residentTotal: 0,
      residentSearchModel: {
        pageNum: 1,
        pageSize: 10,
        communityId: null
      },
      communityList: [],
      rules: {
        moniker: [
          { required: true, message: '请输入对象名称', trigger: 'blur' }
        ],
        phone: [
          { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
        ],
        email: [
          { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
        ],
        communityId: [
          { required: true, message: '请选择所属小区', trigger: 'change' }
        ],
        residentId: [
          { required: true, message: '请选择住户', trigger: 'change' }
        ]
      }
    }
  },
  
  methods: {
    /**
     * 对象类型变化处理
     */
    onObjectTypeChange(type) {
      if (type === 'resident') {
        // 切换到住户模式，清空自定义信息
        this.paymentObjectModel.moniker = ''
        this.paymentObjectModel.phone = ''
        this.paymentObjectModel.residentId = null
        this.residentDisplay = ''
        
        // 更新验证规则
        this.rules.moniker[0].required = false
        this.rules.residentId[0].required = true
      } else {
        // 切换到自定义模式，清空住户信息
        this.paymentObjectModel.residentId = null
        this.residentDisplay = ''
        
        // 更新验证规则
        this.rules.moniker[0].required = true
        this.rules.residentId[0].required = false
      }
    },
    
    /**
     * 打开住户选择器
     */
    openResidentSelector() {
      if (!this.paymentObjectModel.communityId) {
        this.$message.warning('请先选择所属小区')
        return
      }
      
      this.residentSearchModel.communityId = this.paymentObjectModel.communityId
      this.residentSelectorDialog = true
      this.searchResidents()
    },
    
    /**
     * 搜索住户
     */
    searchResidents() {
      const params = {
        ...this.residentSearchModel
      }

      // 如果有搜索关键词，同时搜索姓名和手机号
      if (this.residentSearchKeyword) {
        params.residentName = this.residentSearchKeyword
        params.phone = this.residentSearchKeyword
      }
      
      listCommunityResident(params).then(res => {
        this.residentList = res.data.data.list || []
        this.residentTotal = res.data.data.total || 0
      }).catch(err => {
        console.error('搜索住户失败:', err)
      })
    },
    
    /**
     * 选择住户
     */
    selectResident(resident) {
      this.paymentObjectModel.residentId = resident.id
      this.paymentObjectModel.moniker = resident.residentName
      this.paymentObjectModel.phone = resident.phone
      this.paymentObjectModel.address = resident.address || ''
      this.residentDisplay = `${resident.residentName} (${resident.phone})`
      this.residentSelectorDialog = false
    },
    
    /**
     * 住户分页变化
     */
    residentPageChange(num) {
      this.residentSearchModel.pageNum = num
      this.searchResidents()
    },
    
    /**
     * 加载小区列表
     */
    loadCommunityList() {
      listCommunity({ pageNum: 1, pageSize: 500 }).then(res => {
        this.communityList = res.data.data.list || []
      }).catch(err => {
        console.error('加载小区列表失败:', err)
      })
    },
    
    /**
     * 重置表单
     */
    resetForm() {
      this.$refs.formRef && this.$refs.formRef.resetFields()
      this.paymentObjectModel = {
        id: undefined,
        moniker: '',
        phone: '',
        address: '',
        communityId: getSelectedCommunityId() || null,
        email: '',
        residentId: null
      }
      this.objectType = 'custom'
      this.residentDisplay = ''
      this.residentSearchKeyword = ''
    },
    
    /**
     * 提交表单
     */
    submit() {
      this.$refs.formRef.validate(valid => {
        if (!valid) return
        
        this.loading = true
        const api = this.paymentObjectModel.id ? editPropertyPaymentObject : addPropertyPaymentObject
        
        // 如果是自定义对象，设置residentId为空字符串
        const submitData = { ...this.paymentObjectModel }
        if (this.objectType === 'custom') {
          submitData.residentId = ''
        }
        
        api(submitData).then(() => {
          this.$message.success(this.paymentObjectModel.id ? '修改成功' : '添加成功')
          this.dialog.show = false
          this.$emit('search')
        }).catch(err => {
          this.$message.error(err.data?.errorMessage || '操作失败')
        }).finally(() => {
          this.loading = false
        })
      })
    }
  },
  
  mounted() {
    this.loadCommunityList()
    
    mitt.on('openPaymentObjectAdd', () => {
      this.resetForm()
      this.dialog.title = '新增缴费对象'
      this.dialog.show = true
    })
    
    mitt.on('openPaymentObjectEdit', (data) => {
      this.resetForm()
      this.paymentObjectModel = { ...data }
      
      // 判断对象类型
      if (data.residentId) {
        this.objectType = 'resident'
        this.residentDisplay = data.moniker + (data.phone ? ` (${data.phone})` : '')
        this.rules.moniker[0].required = false
        this.rules.residentId[0].required = true
      } else {
        this.objectType = 'custom'
        this.rules.moniker[0].required = true
        this.rules.residentId[0].required = false
      }
      
      this.dialog.title = '编辑缴费对象'
      this.dialog.show = true
    })
  },
  
  beforeUnmount() {
    mitt.off('openPaymentObjectAdd')
    mitt.off('openPaymentObjectEdit')
  }
}
</script>

<style scoped>
.payment-object-edit-dialog {
  border-radius: 8px;
}

.payment-object-edit-form {
  padding: 0 16px;
}

.resident-selector {
  display: flex;
  align-items: center;
}

.resident-search {
  margin-bottom: 16px;
}

.pagination-col {
  display: flex;
  justify-content: flex-end;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

/* 深色主题适配 */
.dark-theme .payment-object-edit-dialog {
  background-color: var(--card-background);
}
</style>
